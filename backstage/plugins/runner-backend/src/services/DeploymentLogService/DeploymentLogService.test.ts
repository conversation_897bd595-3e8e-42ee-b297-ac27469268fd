import { DeploymentLogService } from './DeploymentLogService';
import { LoggerService } from '@backstage/backend-plugin-api';

describe('DeploymentLogService', () => {
  let service: DeploymentLogService;
  let mockLogger: jest.Mocked<LoggerService>;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
    } as any;

    service = new DeploymentLogService(mockLogger);
  });

  describe('startDeployment', () => {
    it('should create a new deployment log', () => {
      const instanceId = 'test-instance';
      const componentRef = 'Component:default/test-component';

      service.startDeployment(instanceId, componentRef);

      const logs = service.getDeploymentLogs(instanceId);
      expect(logs).toBeDefined();
      expect(logs?.instanceId).toBe(instanceId);
      expect(logs?.componentRef).toBe(componentRef);
      expect(logs?.status).toBe('in-progress');
      expect(logs?.entries).toHaveLength(1);
      expect(logs?.entries[0].step).toBe('Deployment Started');
    });
  });

  describe('addLogEntry', () => {
    it('should add log entries to existing deployment', () => {
      const instanceId = 'test-instance';
      const componentRef = 'Component:default/test-component';

      service.startDeployment(instanceId, componentRef);
      service.addLogEntry(instanceId, 'INFO', 'REPOSITORY', 'Clone Started', 'Cloning repository');

      const logs = service.getDeploymentLogs(instanceId);
      expect(logs?.entries).toHaveLength(2);
      expect(logs?.entries[1].step).toBe('Clone Started');
      expect(logs?.entries[1].category).toBe('REPOSITORY');
    });

    it('should log to standard logger', () => {
      const instanceId = 'test-instance';
      const componentRef = 'Component:default/test-component';

      service.startDeployment(instanceId, componentRef);
      service.addLogEntry(instanceId, 'ERROR', 'DOCKER', 'Build Failed', 'Docker build failed');

      expect(mockLogger.error).toHaveBeenCalledWith(
        '[DOCKER] Build Failed: Docker build failed',
        undefined
      );
    });
  });

  describe('completeDeployment', () => {
    it('should mark deployment as completed', () => {
      const instanceId = 'test-instance';
      const componentRef = 'Component:default/test-component';

      service.startDeployment(instanceId, componentRef);
      service.completeDeployment(instanceId, true);

      const logs = service.getDeploymentLogs(instanceId);
      expect(logs?.status).toBe('completed');
      expect(logs?.endTime).toBeDefined();
    });

    it('should mark deployment as failed', () => {
      const instanceId = 'test-instance';
      const componentRef = 'Component:default/test-component';

      service.startDeployment(instanceId, componentRef);
      service.completeDeployment(instanceId, false);

      const logs = service.getDeploymentLogs(instanceId);
      expect(logs?.status).toBe('failed');
      expect(logs?.endTime).toBeDefined();
    });
  });

  describe('convenience methods', () => {
    beforeEach(() => {
      service.startDeployment('test-instance', 'Component:default/test-component');
    });

    it('should log repository operations', () => {
      service.logRepositoryOperation('test-instance', 'Clone', { repo: 'test-repo' });

      const logs = service.getDeploymentLogs('test-instance');
      const entry = logs?.entries.find(e => e.step === 'Clone');
      expect(entry?.category).toBe('REPOSITORY');
      expect(entry?.details?.repo).toBe('test-repo');
    });

    it('should log Docker operations', () => {
      service.logDockerOperation('test-instance', 'Build', { image: 'test-image' });

      const logs = service.getDeploymentLogs('test-instance');
      const entry = logs?.entries.find(e => e.step === 'Build');
      expect(entry?.category).toBe('DOCKER');
      expect(entry?.details?.image).toBe('test-image');
    });

    it('should log errors', () => {
      const error = new Error('Test error');
      service.logError('test-instance', 'Test Operation', error);

      const logs = service.getDeploymentLogs('test-instance');
      const entry = logs?.entries.find(e => e.step === 'Test Operation');
      expect(entry?.level).toBe('ERROR');
      expect(entry?.category).toBe('ERROR');
      expect(entry?.details?.errorName).toBe('Error');
    });
  });

  describe('cleanup', () => {
    it('should clear deployment logs', () => {
      service.startDeployment('test-instance', 'Component:default/test-component');
      expect(service.getDeploymentLogs('test-instance')).toBeDefined();

      service.clearDeploymentLogs('test-instance');
      expect(service.getDeploymentLogs('test-instance')).toBeUndefined();
    });

    it('should clear all deployment logs', () => {
      service.startDeployment('test-instance-1', 'Component:default/test-component-1');
      service.startDeployment('test-instance-2', 'Component:default/test-component-2');

      expect(service.getAllDeploymentLogs()).toHaveLength(2);

      service.clearAllDeploymentLogs();
      expect(service.getAllDeploymentLogs()).toHaveLength(0);
    });
  });
});
