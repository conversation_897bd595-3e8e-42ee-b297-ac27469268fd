import { LoggerService } from '@backstage/backend-plugin-api';
import { DeploymentLogs, DeploymentLogEntry } from '../RunnerService/types';

export class DeploymentLogService {
  private deploymentLogs = new Map<string, DeploymentLogs>();

  constructor(private logger: LoggerService) {}

  /**
   * Start logging for a new deployment
   */
  startDeployment(instanceId: string, componentRef: string): void {
    const deploymentLog: DeploymentLogs = {
      instanceId,
      componentRef,
      startTime: new Date().toISOString(),
      status: 'in-progress',
      entries: []
    };

    this.deploymentLogs.set(instanceId, deploymentLog);
    this.addLogEntry(instanceId, 'INFO', 'APPLICATION', 'Deployment Started', 
      `Starting deployment for component: ${componentRef}`, { instanceId, componentRef });
  }

  /**
   * Complete a deployment
   */
  completeDeployment(instanceId: string, success: boolean): void {
    const deploymentLog = this.deploymentLogs.get(instanceId);
    if (deploymentLog) {
      deploymentLog.endTime = new Date().toISOString();
      deploymentLog.status = success ? 'completed' : 'failed';
      
      this.addLogEntry(instanceId, success ? 'INFO' : 'ERROR', 'APPLICATION', 
        success ? 'Deployment Completed' : 'Deployment Failed',
        success ? 'Deployment completed successfully' : 'Deployment failed with errors');
    }
  }

  /**
   * Add a log entry for a specific deployment
   */
  addLogEntry(
    instanceId: string,
    level: DeploymentLogEntry['level'],
    category: DeploymentLogEntry['category'],
    step: string,
    message: string,
    details?: Record<string, any>
  ): void {
    const deploymentLog = this.deploymentLogs.get(instanceId);
    if (!deploymentLog) {
      this.logger.warn(`No deployment log found for instance ${instanceId}`);
      return;
    }

    const entry: DeploymentLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      category,
      step,
      message,
      details
    };

    deploymentLog.entries.push(entry);
    
    // Also log to the standard logger for debugging
    const logMessage = `[${category}] ${step}: ${message}`;
    switch (level) {
      case 'ERROR':
        this.logger.error(logMessage, details);
        break;
      case 'WARN':
        this.logger.warn(logMessage, details);
        break;
      case 'DEBUG':
        this.logger.debug(logMessage, details);
        break;
      default:
        this.logger.info(logMessage, details);
    }
  }

  /**
   * Get deployment logs for a specific instance
   */
  getDeploymentLogs(instanceId: string): DeploymentLogs | undefined {
    return this.deploymentLogs.get(instanceId);
  }

  /**
   * Get all deployment logs
   */
  getAllDeploymentLogs(): DeploymentLogs[] {
    return Array.from(this.deploymentLogs.values());
  }

  /**
   * Clear deployment logs for a specific instance
   */
  clearDeploymentLogs(instanceId: string): void {
    this.deploymentLogs.delete(instanceId);
  }

  /**
   * Clear all deployment logs
   */
  clearAllDeploymentLogs(): void {
    this.deploymentLogs.clear();
  }

  /**
   * Log repository operations
   */
  logRepositoryOperation(instanceId: string, operation: string, details: Record<string, any>): void {
    this.addLogEntry(instanceId, 'INFO', 'REPOSITORY', operation, 
      `Repository operation: ${operation}`, details);
  }

  /**
   * Log file system operations
   */
  logFileSystemOperation(instanceId: string, operation: string, details: Record<string, any>): void {
    this.addLogEntry(instanceId, 'INFO', 'FILESYSTEM', operation,
      `File system operation: ${operation}`, details);
  }

  /**
   * Log Docker operations
   */
  logDockerOperation(instanceId: string, operation: string, details: Record<string, any>): void {
    this.addLogEntry(instanceId, 'INFO', 'DOCKER', operation,
      `Docker operation: ${operation}`, details);
  }

  /**
   * Log container operations
   */
  logContainerOperation(instanceId: string, operation: string, details: Record<string, any>): void {
    this.addLogEntry(instanceId, 'INFO', 'CONTAINER', operation,
      `Container operation: ${operation}`, details);
  }

  /**
   * Log application operations
   */
  logApplicationOperation(instanceId: string, operation: string, details: Record<string, any>): void {
    this.addLogEntry(instanceId, 'INFO', 'APPLICATION', operation,
      `Application operation: ${operation}`, details);
  }

  /**
   * Log errors
   */
  logError(instanceId: string, operation: string, error: Error, details?: Record<string, any>): void {
    this.addLogEntry(instanceId, 'ERROR', 'ERROR', operation,
      `Error in ${operation}: ${error.message}`, { 
        ...details, 
        errorName: error.name,
        errorStack: error.stack 
      });
  }

  /**
   * Log warnings
   */
  logWarning(instanceId: string, operation: string, message: string, details?: Record<string, any>): void {
    this.addLogEntry(instanceId, 'WARN', 'ERROR', operation,
      `Warning in ${operation}: ${message}`, details);
  }
}
