import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  IconButton,
  Tooltip,
} from '@material-ui/core';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  GetApp as DownloadIcon,
  Close as CloseIcon,
} from '@material-ui/icons';
import { makeStyles } from '@material-ui/core/styles';
import { useRunner } from '../../hooks/useRunner';
import { RunnerInstance, DeploymentLogs, DeploymentLogEntry } from '../../api/RunnerApi';

const useStyles = makeStyles((theme) => ({
  dialogContent: {
    minWidth: '800px',
    maxWidth: '1200px',
    minHeight: '600px',
  },
  logEntry: {
    marginBottom: theme.spacing(1),
    padding: theme.spacing(1),
    fontFamily: 'monospace',
    fontSize: '12px',
    backgroundColor: '#1e1e1e',
    color: '#ffffff',
    borderLeft: '4px solid',
  },
  logEntryInfo: {
    borderLeftColor: '#2196f3',
  },
  logEntryWarn: {
    borderLeftColor: '#ff9800',
  },
  logEntryError: {
    borderLeftColor: '#f44336',
  },
  logEntryDebug: {
    borderLeftColor: '#9e9e9e',
  },
  categoryChip: {
    marginRight: theme.spacing(1),
    marginBottom: theme.spacing(1),
  },
  timestamp: {
    color: '#888',
    fontSize: '11px',
  },
  logDetails: {
    marginTop: theme.spacing(1),
    padding: theme.spacing(1),
    backgroundColor: '#2a2a2a',
    borderRadius: theme.shape.borderRadius,
    fontSize: '11px',
  },
  statusChip: {
    marginLeft: theme.spacing(1),
  },
  headerActions: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
  },
}));

interface DeploymentLogsDialogProps {
  open: boolean;
  onClose: () => void;
  instance: RunnerInstance;
}

export const DeploymentLogsDialog = ({ open, onClose, instance }: DeploymentLogsDialogProps) => {
  const classes = useStyles();
  const { getDeploymentLogs } = useRunner();
  const [deploymentLogs, setDeploymentLogs] = useState<DeploymentLogs | null>(null);
  const [loading, setLoading] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['APPLICATION']));

  const fetchDeploymentLogs = async (event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    setLoading(true);
    try {
      const logs = await getDeploymentLogs(instance.id);
      if (logs) {
        setDeploymentLogs(logs);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchDeploymentLogs();
    }
  }, [open, instance.id]);

  const handleCategoryToggle = (category: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    event.stopPropagation();
    const newExpanded = new Set(expandedCategories);
    if (isExpanded) {
      newExpanded.add(category);
    } else {
      newExpanded.delete(category);
    }
    setExpandedCategories(newExpanded);
  };

  const handleDownloadLogs = (event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    if (!deploymentLogs) return;

    const logText = deploymentLogs.entries
      .map(entry =>
        `[${entry.timestamp}] [${entry.level}] [${entry.category}] ${entry.step}: ${entry.message}${
          entry.details ? '\n' + JSON.stringify(entry.details, null, 2) : ''
        }`
      )
      .join('\n\n');

    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${instance.componentRef}-${instance.id}-deployment-logs.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleClose = (event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    onClose();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'primary';
      case 'failed': return 'secondary';
      case 'in-progress': return 'default';
      default: return 'default';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'REPOSITORY': return '📁';
      case 'FILESYSTEM': return '📂';
      case 'DOCKER': return '🐳';
      case 'CONTAINER': return '📦';
      case 'APPLICATION': return '🚀';
      case 'ERROR': return '❌';
      default: return '📋';
    }
  };

  const groupLogsByCategory = (entries: DeploymentLogEntry[]) => {
    const grouped = entries.reduce((acc, entry) => {
      if (!acc[entry.category]) {
        acc[entry.category] = [];
      }
      acc[entry.category].push(entry);
      return acc;
    }, {} as Record<string, DeploymentLogEntry[]>);

    return grouped;
  };

  const renderLogEntry = (entry: DeploymentLogEntry) => {
    const levelClass = `logEntry${entry.level.charAt(0) + entry.level.slice(1).toLowerCase()}`;
    
    return (
      <Paper
        key={`${entry.timestamp}-${entry.step}`}
        className={`${classes.logEntry} ${classes[levelClass] || classes.logEntryInfo}`}
      >
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box flex={1}>
            <Typography variant="body2" component="div">
              <strong>{entry.step}</strong>
            </Typography>
            <Typography variant="body2" style={{ marginTop: 4 }}>
              {entry.message}
            </Typography>
          </Box>
          <Box display="flex" flexDirection="column" alignItems="flex-end">
            <Chip 
              label={entry.level} 
              size="small" 
              color={entry.level === 'ERROR' ? 'secondary' : 'default'}
            />
            <Typography className={classes.timestamp} style={{ marginTop: 4 }}>
              {new Date(entry.timestamp).toLocaleTimeString()}
            </Typography>
          </Box>
        </Box>
        {entry.details && Object.keys(entry.details).length > 0 && (
          <Box className={classes.logDetails}>
            <pre>{JSON.stringify(entry.details, null, 2)}</pre>
          </Box>
        )}
      </Paper>
    );
  };

  if (!deploymentLogs && !loading) {
    return null;
  }

  const groupedLogs = deploymentLogs ? groupLogsByCategory(deploymentLogs.entries) : {};
  const categories = Object.keys(groupedLogs).sort();

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        // Only close on escape key, not on backdrop click
        if (reason === 'escapeKeyDown') {
          onClose();
        }
      }}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center">
            <Typography variant="h6">
              Deployment Details - {instance.componentRef}
            </Typography>
            {deploymentLogs && (
              <Chip
                label={deploymentLogs.status.toUpperCase()}
                color={getStatusColor(deploymentLogs.status)}
                size="small"
                className={classes.statusChip}
              />
            )}
          </Box>
          <Box className={classes.headerActions}>
            {loading ? (
              <span>
                <Tooltip title="Refresh logs">
                  <IconButton onClick={fetchDeploymentLogs} disabled={loading}>
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              </span>
            ) : (
              <Tooltip title="Refresh logs">
                <IconButton onClick={fetchDeploymentLogs} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            )}
            {!deploymentLogs ? (
              <span>
                <Tooltip title="Download logs">
                  <IconButton onClick={handleDownloadLogs} disabled={!deploymentLogs}>
                    <DownloadIcon />
                  </IconButton>
                </Tooltip>
              </span>
            ) : (
              <Tooltip title="Download logs">
                <IconButton onClick={handleDownloadLogs} disabled={!deploymentLogs}>
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            )}
            <IconButton onClick={handleClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>
      
      <DialogContent className={classes.dialogContent}>
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
            <Typography variant="body1" style={{ marginLeft: 16 }}>
              Loading deployment logs...
            </Typography>
          </Box>
        ) : deploymentLogs ? (
          <Box>
            {deploymentLogs.startTime && (
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Started: {new Date(deploymentLogs.startTime).toLocaleString()}
                {deploymentLogs.endTime && (
                  <> • Completed: {new Date(deploymentLogs.endTime).toLocaleString()}</>
                )}
              </Typography>
            )}
            
            {categories.map(category => (
              <Accordion
                key={category}
                expanded={expandedCategories.has(category)}
                onChange={handleCategoryToggle(category)}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center">
                    <span style={{ marginRight: 8 }}>{getCategoryIcon(category)}</span>
                    <Typography variant="subtitle1">
                      {category.replace('_', ' ')} ({groupedLogs[category].length} entries)
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box width="100%">
                    {groupedLogs[category].map(renderLogEntry)}
                  </Box>
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
        ) : (
          <Typography variant="body1" color="textSecondary">
            No deployment logs available for this instance.
          </Typography>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};
