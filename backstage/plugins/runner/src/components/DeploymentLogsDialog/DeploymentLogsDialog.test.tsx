import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DeploymentLogsDialog } from './DeploymentLogsDialog';
import { RunnerInstance, DeploymentLogs } from '../../api/RunnerApi';
import { useRunner } from '../../hooks/useRunner';

// Mock the useRunner hook
jest.mock('../../hooks/useRunner');
const mockUseRunner = useRunner as jest.MockedFunction<typeof useRunner>;

describe('DeploymentLogsDialog', () => {
  const mockInstance: RunnerInstance = {
    id: 'test-instance-id',
    componentRef: 'Component:default/test-component',
    status: 'running',
    ports: [3000],
    startedAt: '2023-01-01T00:00:00Z',
  };

  const mockDeploymentLogs: DeploymentLogs = {
    instanceId: 'test-instance-id',
    componentRef: 'Component:default/test-component',
    startTime: '2023-01-01T00:00:00Z',
    endTime: '2023-01-01T00:05:00Z',
    status: 'completed',
    entries: [
      {
        timestamp: '2023-01-01T00:00:00Z',
        level: 'INFO',
        category: 'APPLICATION',
        step: 'Deployment Started',
        message: 'Starting deployment for component: Component:default/test-component',
        details: { instanceId: 'test-instance-id' }
      },
      {
        timestamp: '2023-01-01T00:01:00Z',
        level: 'INFO',
        category: 'REPOSITORY',
        step: 'Repository Analysis',
        message: 'Analyzing source location annotation',
        details: { sourceLocation: 'https://github.com/test/repo' }
      },
      {
        timestamp: '2023-01-01T00:02:00Z',
        level: 'INFO',
        category: 'DOCKER',
        step: 'Docker Build Started',
        message: 'Starting Docker image build process',
        details: { dockerfile: './Dockerfile' }
      },
      {
        timestamp: '2023-01-01T00:04:00Z',
        level: 'INFO',
        category: 'CONTAINER',
        step: 'Container Started',
        message: 'Successfully started Docker container',
        details: { containerId: 'abc123', ports: [3000] }
      },
      {
        timestamp: '2023-01-01T00:05:00Z',
        level: 'INFO',
        category: 'APPLICATION',
        step: 'Deployment Completed',
        message: 'Deployment completed successfully'
      }
    ]
  };

  const mockGetDeploymentLogs = jest.fn();

  beforeEach(() => {
    mockUseRunner.mockReturnValue({
      startComponent: jest.fn(),
      stopComponent: jest.fn(),
      getStatus: jest.fn(),
      getLogs: jest.fn(),
      getDeploymentLogs: mockGetDeploymentLogs,
      loading: false,
    });

    mockGetDeploymentLogs.mockResolvedValue(mockDeploymentLogs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render dialog when open', async () => {
    render(
      <DeploymentLogsDialog
        open={true}
        onClose={jest.fn()}
        instance={mockInstance}
      />
    );

    expect(screen.getByText('Deployment Details - Component:default/test-component')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('COMPLETED')).toBeInTheDocument();
    });
  });

  it('should fetch deployment logs when opened', async () => {
    render(
      <DeploymentLogsDialog
        open={true}
        onClose={jest.fn()}
        instance={mockInstance}
      />
    );

    await waitFor(() => {
      expect(mockGetDeploymentLogs).toHaveBeenCalledWith('test-instance-id');
    });
  });

  it('should display log categories as accordions', async () => {
    render(
      <DeploymentLogsDialog
        open={true}
        onClose={jest.fn()}
        instance={mockInstance}
      />
    );

    await waitFor(() => {
      expect(screen.getByText(/APPLICATION \(2 entries\)/)).toBeInTheDocument();
      expect(screen.getByText(/REPOSITORY \(1 entries\)/)).toBeInTheDocument();
      expect(screen.getByText(/DOCKER \(1 entries\)/)).toBeInTheDocument();
      expect(screen.getByText(/CONTAINER \(1 entries\)/)).toBeInTheDocument();
    });
  });

  it('should expand APPLICATION category by default', async () => {
    render(
      <DeploymentLogsDialog
        open={true}
        onClose={jest.fn()}
        instance={mockInstance}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Deployment Started')).toBeInTheDocument();
      expect(screen.getByText('Deployment Completed')).toBeInTheDocument();
    });
  });

  it('should allow expanding and collapsing categories', async () => {
    render(
      <DeploymentLogsDialog
        open={true}
        onClose={jest.fn()}
        instance={mockInstance}
      />
    );

    await waitFor(() => {
      const repositoryAccordion = screen.getByText(/REPOSITORY \(1 entries\)/);
      fireEvent.click(repositoryAccordion);
    });

    await waitFor(() => {
      expect(screen.getByText('Repository Analysis')).toBeInTheDocument();
    });
  });

  it('should display log entry details', async () => {
    render(
      <DeploymentLogsDialog
        open={true}
        onClose={jest.fn()}
        instance={mockInstance}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Starting deployment for component: Component:default/test-component')).toBeInTheDocument();
    });
  });

  it('should handle refresh button click', async () => {
    render(
      <DeploymentLogsDialog
        open={true}
        onClose={jest.fn()}
        instance={mockInstance}
      />
    );

    // Wait for initial load to complete
    await waitFor(() => {
      expect(screen.getByText('COMPLETED')).toBeInTheDocument();
    });

    const refreshButton = screen.getByTitle('Refresh logs');
    fireEvent.click(refreshButton);

    await waitFor(() => {
      expect(mockGetDeploymentLogs).toHaveBeenCalledTimes(2);
    });
  });

  it('should handle close button click', () => {
    const mockOnClose = jest.fn();
    
    render(
      <DeploymentLogsDialog
        open={true}
        onClose={mockOnClose}
        instance={mockInstance}
      />
    );

    const closeButton = screen.getByRole('button', { name: 'Close' });
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should show loading state', () => {
    mockGetDeploymentLogs.mockImplementation(() => new Promise(() => {})); // Never resolves

    render(
      <DeploymentLogsDialog
        open={true}
        onClose={jest.fn()}
        instance={mockInstance}
      />
    );

    expect(screen.getByText('Loading deployment logs...')).toBeInTheDocument();
  });

  it('should show download button when logs are available', async () => {
    render(
      <DeploymentLogsDialog
        open={true}
        onClose={jest.fn()}
        instance={mockInstance}
      />
    );

    // Wait for initial load to complete
    await waitFor(() => {
      expect(screen.getByText('COMPLETED')).toBeInTheDocument();
    });

    // Check that download button is present and enabled
    const downloadButton = screen.getByTitle('Download logs');
    expect(downloadButton).toBeInTheDocument();
    expect(downloadButton).not.toBeDisabled();
  });
});
